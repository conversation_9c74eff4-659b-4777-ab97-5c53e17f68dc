#!/bin/bash

# Script de Deploy Simples - Amvox Omnichannel
# Este script faz o deploy completo da aplicação e cria o usuário admin

set -e

echo "🚀 Iniciando deploy do Amvox Omnichannel..."

# Função para aguardar serviço ficar disponível
wait_for_service() {
    local service_name=$1
    local url=$2
    local max_attempts=30
    local attempt=1
    
    echo "⏳ Aguardando $service_name ficar disponível..."
    
    while [ $attempt -le $max_attempts ]; do
        if curl -s -f "$url" > /dev/null 2>&1; then
            echo "✅ $service_name está disponível!"
            return 0
        fi
        
        echo "🔄 Tentativa $attempt/$max_attempts - Aguardando $service_name..."
        sleep 5
        attempt=$((attempt + 1))
    done
    
    echo "❌ $service_name não ficou disponível após $max_attempts tentativas"
    return 1
}

# Função para criar usuário admin
create_admin_user() {
    echo "👤 Criando usuário administrador..."
    
    # Aguardar PostgreSQL estar pronto
    echo "🐘 Verificando PostgreSQL..."
    docker exec amvox_postgres pg_isready -U amvox_user || {
        echo "❌ PostgreSQL não está pronto"
        return 1
    }
    
    # Deletar usuário admin existente (se houver)
    echo "🗑️ Removendo usuário admin existente (se houver)..."
    docker exec amvox_postgres psql -U amvox_user -d amvox_db -c "DELETE FROM usuarios WHERE login = 'admin';" || true
    
    # Criar novo usuário admin com senha hash conhecida
    echo "➕ Criando novo usuário admin..."
    docker exec amvox_postgres psql -U amvox_user -d amvox_db -c "
        INSERT INTO usuarios (nome, sobrenome, login, senha, nivel_usuario, email_corporativo, senha_email_corporativo, ativo) 
        VALUES (
            'Administrador', 
            'Sistema', 
            'admin', 
            '\$2b\$12\$EixZaYVK1fsbw1ZfbX3OXePaWxn96p36WQoeG6Lruj3vjPGga31lW', 
            'ADMINISTRADOR', 
            '<EMAIL>', 
            'admin123', 
            true
        );
    " && echo "✅ Usuário admin criado com sucesso!" || {
        echo "❌ Erro ao criar usuário admin"
        return 1
    }
    
    # Verificar se o usuário foi criado
    echo "🔍 Verificando usuário criado..."
    docker exec amvox_postgres psql -U amvox_user -d amvox_db -c "SELECT id, nome, login, nivel_usuario FROM usuarios WHERE login = 'admin';"
}

# Parar containers existentes
echo "⏹️ Parando containers existentes..."
docker-compose down --volumes --remove-orphans

# Limpar sistema Docker
echo "🧹 Limpando sistema Docker..."
docker system prune -af --volumes

# Construir novas imagens
echo "🔨 Construindo novas imagens..."
docker-compose build --no-cache

# Iniciar serviços de infraestrutura primeiro
echo "🗄️ Iniciando serviços de infraestrutura..."
docker-compose up -d postgres redis

# Aguardar PostgreSQL e Redis
echo "⏳ Aguardando PostgreSQL e Redis..."
sleep 15

# Verificar se PostgreSQL está pronto
docker exec amvox_postgres pg_isready -U amvox_user || {
    echo "❌ PostgreSQL não está pronto"
    exit 1
}

# Iniciar backend
echo "🔧 Iniciando backend..."
docker-compose up -d backend

# Aguardar backend ficar disponível
wait_for_service "Backend" "http://localhost:8001/health"

# Criar usuário admin
create_admin_user

# Iniciar frontend e nginx
echo "🌐 Iniciando frontend e nginx..."
docker-compose up -d frontend nginx

# Aguardar frontend ficar disponível
wait_for_service "Frontend" "http://localhost:3000"

# Verificar status final
echo "📊 Verificando status dos serviços..."
docker-compose ps

echo ""
echo "🎉 Deploy concluído com sucesso!"
echo "=================================="
echo "📱 Frontend: http://localhost:3000"
echo "🔧 Backend: http://localhost:8001"
echo "📊 Health Check: http://localhost:8001/health"
echo "🗄️ PostgreSQL: localhost:5432"
echo "🔴 Redis: localhost:6379"
echo ""
echo "👤 Credenciais do Administrador:"
echo "   Login: admin"
echo "   Senha: secret"
echo "=================================="
