#!/bin/bash

# Script de Deploy Rápido - Amvox Omnichannel
# Para desenvolvimento e testes rápidos

set -e

echo "🚀 Deploy Rápido - Amvox Omnichannel"
echo "===================================="

# Parar containers
echo "⏹️ Parando containers..."
docker-compose down

# Iniciar containers
echo "▶️ Iniciando containers..."
docker-compose up -d

# Aguardar backend
echo "⏳ Aguardando backend..."
sleep 20

# Verificar se está funcionando
echo "🔍 Verificando serviços..."

# Backend
if curl -s http://localhost:8001/health > /dev/null; then
    echo "✅ Backend: OK"
else
    echo "❌ Backend: FALHA"
fi

# Frontend
if curl -s http://localhost:3000 > /dev/null; then
    echo "✅ Frontend: OK"
else
    echo "❌ Frontend: FALHA"
fi

# Verificar usuário admin
echo "👤 Verificando usuário admin..."
docker exec amvox_postgres psql -U amvox_user -d amvox_db -c "SELECT login, nivel_usuario FROM usuarios WHERE login = 'admin';" || echo "⚠️ Usuário admin não encontrado"

echo ""
echo "🎉 Deploy rápido concluído!"
echo "📱 Frontend: http://localhost:3000"
echo "🔧 Backend: http://localhost:8001"
echo "👤 Login: admin | Senha: secret"
