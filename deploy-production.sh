#!/bin/bash

# Script de Deploy para Produção - Amvox Omnichannel
# Este script faz o deploy completo com verificações de saúde

set -e

# Configurações
COMPOSE_FILE="docker-compose.yml"
ADMIN_LOGIN="admin"
ADMIN_PASSWORD="secret"

# Cores
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
NC='\033[0m'

# Funções de log
log() {
    echo -e "${BLUE}[$(date +'%H:%M:%S')]${NC} $1"
}

success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

error() {
    echo -e "${RED}[ERROR]${NC} $1"
    exit 1
}

warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

# Banner
show_banner() {
    echo -e "${PURPLE}"
    echo "╔══════════════════════════════════════════════════════════════╗"
    echo "║                    AMVOX OMNICHANNEL                         ║"
    echo "║                   Deploy para Produção                      ║"
    echo "║                        v1.0.0                               ║"
    echo "╚══════════════════════════════════════════════════════════════╝"
    echo -e "${NC}"
}

# Verificar pré-requisitos
check_prerequisites() {
    log "Verificando pré-requisitos..."
    
    # Verificar Docker
    if ! command -v docker &> /dev/null; then
        error "Docker não está instalado!"
    fi
    
    # Verificar Docker Compose
    if ! command -v docker-compose &> /dev/null; then
        error "Docker Compose não está instalado!"
    fi
    
    # Verificar curl
    if ! command -v curl &> /dev/null; then
        error "curl não está instalado!"
    fi
    
    success "Pré-requisitos verificados!"
}

# Aguardar serviço ficar disponível
wait_for_service() {
    local service_name=$1
    local url=$2
    local max_attempts=60
    local attempt=1
    
    log "Aguardando $service_name ficar disponível..."
    
    while [ $attempt -le $max_attempts ]; do
        if curl -s -f "$url" > /dev/null 2>&1; then
            success "$service_name está disponível!"
            return 0
        fi
        
        if [ $((attempt % 10)) -eq 0 ]; then
            log "Tentativa $attempt/$max_attempts - Aguardando $service_name..."
        fi
        
        sleep 3
        attempt=$((attempt + 1))
    done
    
    error "$service_name não ficou disponível após $max_attempts tentativas"
}

# Verificar saúde do PostgreSQL
check_postgres() {
    log "Verificando PostgreSQL..."
    
    local max_attempts=20
    local attempt=1
    
    while [ $attempt -le $max_attempts ]; do
        if docker exec amvox_postgres pg_isready -U amvox_user > /dev/null 2>&1; then
            success "PostgreSQL está pronto!"
            return 0
        fi
        
        log "Aguardando PostgreSQL... ($attempt/$max_attempts)"
        sleep 3
        attempt=$((attempt + 1))
    done
    
    error "PostgreSQL não ficou pronto!"
}

# Criar usuário admin
create_admin_user() {
    log "Criando usuário administrador..."
    
    # Verificar se PostgreSQL está pronto
    check_postgres
    
    # Executar script de criação do admin
    if docker exec amvox_backend python create_admin.py; then
        success "Usuário admin criado com sucesso!"
    else
        warning "Falha ao executar script Python, tentando via SQL..."
        
        # Fallback: criar via SQL direto
        docker exec amvox_postgres psql -U amvox_user -d amvox_db -c "
            DELETE FROM usuarios WHERE login = 'admin';
            INSERT INTO usuarios (nome, sobrenome, login, senha, nivel_usuario, email_corporativo, senha_email_corporativo, ativo) 
            VALUES (
                'Administrador', 
                'Sistema', 
                'admin', 
                '\$2b\$12\$EixZaYVK1fsbw1ZfbX3OXePaWxn96p36WQoeG6Lruj3vjPGga31lW', 
                'ADMINISTRADOR', 
                '<EMAIL>', 
                'admin123', 
                true
            );
        " && success "Usuário admin criado via SQL!" || error "Falha ao criar usuário admin!"
    fi
}

# Verificar login do admin
test_admin_login() {
    log "Testando login do administrador..."
    
    local response=$(curl -s -X POST http://localhost:8001/api/v1/auth/login \
        -H "Content-Type: application/x-www-form-urlencoded" \
        -d "username=$ADMIN_LOGIN&password=$ADMIN_PASSWORD" \
        -w "%{http_code}")
    
    local http_code="${response: -3}"
    
    if [ "$http_code" = "200" ]; then
        success "Login do administrador funcionando!"
    else
        error "Falha no login do administrador (HTTP: $http_code)"
    fi
}

# Verificar todos os serviços
health_check() {
    log "Executando verificações de saúde..."
    
    # Backend
    wait_for_service "Backend" "http://localhost:8001/health"
    
    # Frontend
    wait_for_service "Frontend" "http://localhost:3000"
    
    # Testar login
    test_admin_login
    
    success "Todas as verificações de saúde passaram!"
}

# Deploy principal
main_deploy() {
    show_banner
    
    log "Iniciando deploy do Amvox Omnichannel..."
    
    # Verificar pré-requisitos
    check_prerequisites
    
    # Parar containers existentes
    log "Parando containers existentes..."
    docker-compose -f $COMPOSE_FILE down --volumes --remove-orphans
    
    # Limpar sistema
    log "Limpando sistema Docker..."
    docker system prune -af --volumes
    
    # Construir imagens
    log "Construindo imagens..."
    docker-compose -f $COMPOSE_FILE build --no-cache
    
    # Iniciar infraestrutura
    log "Iniciando serviços de infraestrutura..."
    docker-compose -f $COMPOSE_FILE up -d postgres redis
    
    # Aguardar infraestrutura
    sleep 10
    check_postgres
    
    # Iniciar backend
    log "Iniciando backend..."
    docker-compose -f $COMPOSE_FILE up -d backend
    
    # Aguardar backend
    wait_for_service "Backend" "http://localhost:8001/health"
    
    # Criar usuário admin
    create_admin_user
    
    # Iniciar frontend e nginx
    log "Iniciando frontend e nginx..."
    docker-compose -f $COMPOSE_FILE up -d frontend nginx
    
    # Verificações finais
    health_check
    
    # Status final
    log "Status dos containers:"
    docker-compose -f $COMPOSE_FILE ps
    
    # Sucesso
    echo ""
    success "Deploy concluído com sucesso!"
    echo "=================================="
    echo "📱 Frontend: http://localhost:3000"
    echo "🔧 Backend: http://localhost:8001"
    echo "📊 Health: http://localhost:8001/health"
    echo ""
    echo "👤 Credenciais do Administrador:"
    echo "   Login: $ADMIN_LOGIN"
    echo "   Senha: $ADMIN_PASSWORD"
    echo "=================================="
}

# Executar deploy
main_deploy
