"""
Modelos de banco de dados para WhatsApp.
"""

from datetime import datetime
from sqlalchemy import Column, Integer, String, DateTime, Text, Boolean, ForeignKey, Enum
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
import enum
from app.database.connection import Base


class StatusMensagem(enum.Enum):
    """Status da mensagem WhatsApp."""
    ENVIADA = "enviada"
    ENTREGUE = "entregue"
    LIDA = "lida"
    ERRO = "erro"


class TipoMensagem(enum.Enum):
    """Tipo da mensagem WhatsApp."""
    TEXTO = "texto"
    IMAGEM = "imagem"
    AUDIO = "audio"
    VIDEO = "video"
    DOCUMENTO = "documento"
    STICKER = "sticker"
    LOCALIZACAO = "localizacao"


class StatusConversa(enum.Enum):
    """Status da conversa WhatsApp."""
    ATIVA = "ativa"
    AGUARDANDO = "aguardando"
    FINALIZADA = "finalizada"
    TRANSFERIDA = "transferida"


class WhatsAppConversa(Base):
    """Modelo para conversas do WhatsApp."""
    
    __tablename__ = "whatsapp_conversas"
    
    id = Column(Integer, primary_key=True, index=True, autoincrement=True)
    numero_contato = Column(String(20), nullable=False, index=True, comment="Número do contato (formato internacional)")
    nome_contato = Column(String(255), nullable=True, comment="Nome do contato")
    instancia_id = Column(String(100), nullable=False, comment="ID da instância Evolution API")
    
    # Status e atribuição
    status = Column(
        Enum(StatusConversa),
        nullable=False,
        default=StatusConversa.AGUARDANDO,
        comment="Status atual da conversa"
    )
    colaborador_id = Column(
        Integer,
        ForeignKey("usuarios.id"),
        nullable=True,
        comment="ID do colaborador responsável"
    )
    
    # Metadados da conversa
    ultima_mensagem = Column(Text, nullable=True, comment="Texto da última mensagem")
    ultima_mensagem_em = Column(DateTime(timezone=True), nullable=True, comment="Data/hora da última mensagem")
    mensagens_nao_lidas = Column(Integer, default=0, comment="Quantidade de mensagens não lidas")
    
    # Campos de controle
    criada_em = Column(
        DateTime(timezone=True),
        server_default=func.now(),
        nullable=False,
        comment="Data e hora de criação"
    )
    atualizada_em = Column(
        DateTime(timezone=True),
        server_default=func.now(),
        onupdate=func.now(),
        nullable=False,
        comment="Data e hora da última atualização"
    )
    finalizada_em = Column(DateTime(timezone=True), nullable=True, comment="Data/hora de finalização")
    
    # Relacionamentos
    colaborador = relationship("Usuario", foreign_keys=[colaborador_id])
    mensagens = relationship("WhatsAppMensagem", back_populates="conversa", cascade="all, delete-orphan")
    
    def __repr__(self):
        return f"<WhatsAppConversa(id={self.id}, numero='{self.numero_contato}', status='{self.status.value}')>"


class WhatsAppMensagem(Base):
    """Modelo para mensagens do WhatsApp."""
    
    __tablename__ = "whatsapp_mensagens"
    
    id = Column(Integer, primary_key=True, index=True, autoincrement=True)
    conversa_id = Column(Integer, ForeignKey("whatsapp_conversas.id"), nullable=False, comment="ID da conversa")
    
    # Identificadores Evolution API
    message_id = Column(String(255), unique=True, nullable=False, index=True, comment="ID único da mensagem na Evolution API")
    remote_jid = Column(String(255), nullable=False, comment="JID remoto (nú*******************)")
    
    # Conteúdo da mensagem
    tipo = Column(
        Enum(TipoMensagem),
        nullable=False,
        default=TipoMensagem.TEXTO,
        comment="Tipo da mensagem"
    )
    conteudo = Column(Text, nullable=True, comment="Conteúdo textual da mensagem")
    midia_url = Column(String(500), nullable=True, comment="URL da mídia (se aplicável)")
    midia_tipo = Column(String(100), nullable=True, comment="Tipo MIME da mídia")
    
    # Metadados
    de_contato = Column(Boolean, nullable=False, default=True, comment="True se enviada pelo contato, False se pelo colaborador")
    nome_remetente = Column(String(255), nullable=True, comment="Nome do remetente")
    
    # Status e controle
    status = Column(
        Enum(StatusMensagem),
        nullable=False,
        default=StatusMensagem.ENVIADA,
        comment="Status da mensagem"
    )
    lida = Column(Boolean, default=False, comment="Se a mensagem foi lida pelo colaborador")
    
    # Timestamps
    timestamp_evolution = Column(DateTime(timezone=True), nullable=False, comment="Timestamp da Evolution API")
    criada_em = Column(
        DateTime(timezone=True),
        server_default=func.now(),
        nullable=False,
        comment="Data e hora de criação no sistema"
    )
    lida_em = Column(DateTime(timezone=True), nullable=True, comment="Data/hora em que foi marcada como lida")
    
    # Relacionamentos
    conversa = relationship("WhatsAppConversa", back_populates="mensagens")
    
    def __repr__(self):
        return f"<WhatsAppMensagem(id={self.id}, message_id='{self.message_id}', tipo='{self.tipo.value}')>"


class WhatsAppInstancia(Base):
    """Modelo para instâncias da Evolution API."""
    
    __tablename__ = "whatsapp_instancias"
    
    id = Column(Integer, primary_key=True, index=True, autoincrement=True)
    nome = Column(String(100), nullable=False, unique=True, comment="Nome da instância")
    instancia_id = Column(String(100), nullable=False, unique=True, comment="ID da instância na Evolution API")
    
    # Configurações
    api_key = Column(String(255), nullable=False, comment="API Key da Evolution API")
    base_url = Column(String(255), nullable=False, comment="URL base da Evolution API")
    webhook_url = Column(String(500), nullable=True, comment="URL do webhook configurada")
    
    # Status
    ativa = Column(Boolean, default=True, comment="Se a instância está ativa")
    conectada = Column(Boolean, default=False, comment="Se está conectada ao WhatsApp")
    ultimo_status = Column(String(50), nullable=True, comment="Último status reportado")
    
    # Campos de controle
    criada_em = Column(
        DateTime(timezone=True),
        server_default=func.now(),
        nullable=False,
        comment="Data e hora de criação"
    )
    atualizada_em = Column(
        DateTime(timezone=True),
        server_default=func.now(),
        onupdate=func.now(),
        nullable=False,
        comment="Data e hora da última atualização"
    )
    
    def __repr__(self):
        return f"<WhatsAppInstancia(id={self.id}, nome='{self.nome}', conectada={self.conectada})>"
