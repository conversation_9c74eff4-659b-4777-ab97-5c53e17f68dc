"""
CRUD operations para WhatsApp.
"""

from typing import List, Optional
from sqlalchemy.orm import Session
from sqlalchemy import desc

from app.models.whatsapp import (
    WhatsAppConversa,
    WhatsAppMensagem,
    WhatsAppInstancia,
    StatusConversa,
    StatusMensagem
)
from app.schemas.whatsapp import (
    WhatsAppConversaCreate,
    WhatsAppConversaUpdate,
    WhatsAppMensagemCreate,
    WhatsAppInstanciaCreate
)


class WhatsAppCRUD:
    """CRUD operations para WhatsApp."""
    
    def __init__(self, db: Session):
        self.db = db
    
    # ==================== CONVERSAS ====================
    
    def create_conversa(self, conversa: WhatsAppConversaCreate) -> WhatsAppConversa:
        """Cria uma nova conversa."""
        db_conversa = WhatsAppConversa(**conversa.dict())
        self.db.add(db_conversa)
        self.db.commit()
        self.db.refresh(db_conversa)
        return db_conversa
    
    def get_conversa(self, conversa_id: int) -> Optional[WhatsAppConversa]:
        """Obtém uma conversa por ID."""
        return self.db.query(WhatsAppConversa).filter(
            WhatsAppConversa.id == conversa_id
        ).first()
    
    def get_conversa_by_numero(
        self, 
        numero_contato: str, 
        instancia_id: str
    ) -> Optional[WhatsAppConversa]:
        """Obtém uma conversa por número e instância."""
        return self.db.query(WhatsAppConversa).filter(
            WhatsAppConversa.numero_contato == numero_contato,
            WhatsAppConversa.instancia_id == instancia_id
        ).first()
    
    def get_conversas(
        self,
        skip: int = 0,
        limit: int = 100,
        status: Optional[StatusConversa] = None,
        colaborador_id: Optional[int] = None
    ) -> List[WhatsAppConversa]:
        """Lista conversas com filtros."""
        query = self.db.query(WhatsAppConversa)
        
        if status:
            query = query.filter(WhatsAppConversa.status == status)
        
        if colaborador_id:
            query = query.filter(WhatsAppConversa.colaborador_id == colaborador_id)
        
        return query.order_by(
            desc(WhatsAppConversa.ultima_mensagem_em)
        ).offset(skip).limit(limit).all()
    
    def update_conversa(
        self, 
        conversa_id: int, 
        conversa_update: WhatsAppConversaUpdate
    ) -> Optional[WhatsAppConversa]:
        """Atualiza uma conversa."""
        db_conversa = self.get_conversa(conversa_id)
        if not db_conversa:
            return None
        
        update_data = conversa_update.dict(exclude_unset=True)
        for field, value in update_data.items():
            setattr(db_conversa, field, value)
        
        self.db.commit()
        self.db.refresh(db_conversa)
        return db_conversa
    
    def delete_conversa(self, conversa_id: int) -> bool:
        """Deleta uma conversa."""
        db_conversa = self.get_conversa(conversa_id)
        if not db_conversa:
            return False
        
        self.db.delete(db_conversa)
        self.db.commit()
        return True
    
    # ==================== MENSAGENS ====================
    
    def create_mensagem(self, mensagem: WhatsAppMensagemCreate) -> WhatsAppMensagem:
        """Cria uma nova mensagem."""
        db_mensagem = WhatsAppMensagem(**mensagem.dict())
        self.db.add(db_mensagem)
        self.db.commit()
        self.db.refresh(db_mensagem)
        return db_mensagem
    
    def get_mensagem(self, mensagem_id: int) -> Optional[WhatsAppMensagem]:
        """Obtém uma mensagem por ID."""
        return self.db.query(WhatsAppMensagem).filter(
            WhatsAppMensagem.id == mensagem_id
        ).first()
    
    def get_mensagem_by_message_id(self, message_id: str) -> Optional[WhatsAppMensagem]:
        """Obtém uma mensagem por message_id da Evolution API."""
        return self.db.query(WhatsAppMensagem).filter(
            WhatsAppMensagem.message_id == message_id
        ).first()
    
    def get_mensagens_conversa(
        self,
        conversa_id: int,
        skip: int = 0,
        limit: int = 50
    ) -> List[WhatsAppMensagem]:
        """Lista mensagens de uma conversa."""
        return self.db.query(WhatsAppMensagem).filter(
            WhatsAppMensagem.conversa_id == conversa_id
        ).order_by(
            WhatsAppMensagem.timestamp_evolution
        ).offset(skip).limit(limit).all()
    
    def mark_mensagens_as_read(self, conversa_id: int) -> int:
        """Marca mensagens como lidas e retorna quantidade atualizada."""
        updated = self.db.query(WhatsAppMensagem).filter(
            WhatsAppMensagem.conversa_id == conversa_id,
            WhatsAppMensagem.lida == False,
            WhatsAppMensagem.de_contato == True
        ).update({
            WhatsAppMensagem.lida: True
        })
        
        self.db.commit()
        return updated
    
    # ==================== INSTÂNCIAS ====================
    
    def create_instancia(self, instancia: WhatsAppInstanciaCreate) -> WhatsAppInstancia:
        """Cria uma nova instância."""
        db_instancia = WhatsAppInstancia(**instancia.dict())
        self.db.add(db_instancia)
        self.db.commit()
        self.db.refresh(db_instancia)
        return db_instancia
    
    def get_instancia(self, instancia_id: int) -> Optional[WhatsAppInstancia]:
        """Obtém uma instância por ID."""
        return self.db.query(WhatsAppInstancia).filter(
            WhatsAppInstancia.id == instancia_id
        ).first()
    
    def get_instancia_by_name(self, instancia_id: str) -> Optional[WhatsAppInstancia]:
        """Obtém uma instância por nome/ID da Evolution API."""
        return self.db.query(WhatsAppInstancia).filter(
            WhatsAppInstancia.instancia_id == instancia_id
        ).first()
    
    def get_instancias_ativas(self) -> List[WhatsAppInstancia]:
        """Lista instâncias ativas."""
        return self.db.query(WhatsAppInstancia).filter(
            WhatsAppInstancia.ativa == True
        ).all()
    
    def get_primeira_instancia_ativa(self) -> Optional[WhatsAppInstancia]:
        """Obtém a primeira instância ativa."""
        return self.db.query(WhatsAppInstancia).filter(
            WhatsAppInstancia.ativa == True
        ).first()
    
    def update_instancia_status(
        self, 
        instancia_id: int, 
        conectada: bool, 
        ultimo_status: str
    ) -> Optional[WhatsAppInstancia]:
        """Atualiza status de uma instância."""
        db_instancia = self.get_instancia(instancia_id)
        if not db_instancia:
            return None
        
        db_instancia.conectada = conectada
        db_instancia.ultimo_status = ultimo_status
        
        self.db.commit()
        self.db.refresh(db_instancia)
        return db_instancia
    
    # ==================== ESTATÍSTICAS ====================
    
    def get_estatisticas(self) -> dict:
        """Obtém estatísticas gerais do WhatsApp."""
        total_conversas = self.db.query(WhatsAppConversa).count()
        conversas_ativas = self.db.query(WhatsAppConversa).filter(
            WhatsAppConversa.status == StatusConversa.ATIVA
        ).count()
        conversas_aguardando = self.db.query(WhatsAppConversa).filter(
            WhatsAppConversa.status == StatusConversa.AGUARDANDO
        ).count()
        
        total_mensagens = self.db.query(WhatsAppMensagem).count()
        mensagens_nao_lidas = self.db.query(WhatsAppMensagem).filter(
            WhatsAppMensagem.lida == False,
            WhatsAppMensagem.de_contato == True
        ).count()
        
        total_instancias = self.db.query(WhatsAppInstancia).count()
        instancias_ativas = self.db.query(WhatsAppInstancia).filter(
            WhatsAppInstancia.ativa == True
        ).count()
        instancias_conectadas = self.db.query(WhatsAppInstancia).filter(
            WhatsAppInstancia.conectada == True
        ).count()
        
        return {
            "conversas": {
                "total": total_conversas,
                "ativas": conversas_ativas,
                "aguardando": conversas_aguardando
            },
            "mensagens": {
                "total": total_mensagens,
                "nao_lidas": mensagens_nao_lidas
            },
            "instancias": {
                "total": total_instancias,
                "ativas": instancias_ativas,
                "conectadas": instancias_conectadas
            }
        }


# Função helper para criar instância do CRUD
def get_whatsapp_crud(db: Session) -> WhatsAppCRUD:
    """Cria uma instância do CRUD WhatsApp."""
    return WhatsAppCRUD(db)
