import logging
from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from contextlib import asynccontextmanager

from app.api.v1 import threecx, reports, pdf_reports, outlook, mock_data, auth, whatsapp
from app.database.connection import create_tables, SessionLocal
from app.crud.configuracao import get_configuracoes_microsoft_graph
from app.config.settings import settings

# Configurar logging
logging.basicConfig(
    level=logging.DEBUG if settings.DEBUG else logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler()
    ]
)

logger = logging.getLogger(__name__)

@asynccontextmanager
async def lifespan(app: FastAPI):
    """Gerenciar ciclo de vida da aplicação."""
    # Startup
    print("🚀 Iniciando Amvox Omnichannel API...")
    try:
        create_tables()
        print("✅ Tabelas do banco de dados verificadas/criadas!")

        # Criar usuário admin padrão (sempre recria para garantir consistência)
        try:
            from app.utils.security import hash_password
            from app.database.models import Usuario, NivelUsuario

            db = SessionLocal()

            # Remover usuário admin existente (se houver)
            existing_admin = db.query(Usuario).filter(Usuario.login == "admin").first()
            if existing_admin:
                print("🗑️ Removendo usuário admin existente...")
                db.delete(existing_admin)
                db.commit()

            # Criar novo usuário admin com credenciais padrão
            print("➕ Criando usuário administrador padrão...")

            admin_user = Usuario(
                nome="Administrador",
                sobrenome="Sistema",
                login="admin",
                senha=hash_password("secret"),  # Senha padrão: secret
                nivel_usuario=NivelUsuario.ADMINISTRADOR,
                email_corporativo="<EMAIL>",
                senha_email_corporativo="admin123",
                ativo=True
            )

            db.add(admin_user)
            db.commit()
            db.refresh(admin_user)

            print("✅ Usuário admin criado com sucesso!")
            print(f"   Login: admin")
            print(f"   Senha: secret")
            print(f"   ID: {admin_user.id}")

            db.close()
        except Exception as e:
            print(f"❌ Erro ao criar usuário admin: {e}")
            if 'db' in locals():
                try:
                    db.rollback()
                    db.close()
                except:
                    pass

        # Carregar configurações do Microsoft Graph do banco
        try:
            db = SessionLocal()
            configs = get_configuracoes_microsoft_graph(db)

            if configs.get("microsoft_graph_client_id"):
                from app.config.settings import settings
                settings.MICROSOFT_CLIENT_ID = configs.get("microsoft_graph_client_id")
                settings.MICROSOFT_CLIENT_SECRET = configs.get("microsoft_graph_client_secret")
                settings.MICROSOFT_TENANT_ID = configs.get("microsoft_graph_tenant_id")
                print("✅ Configurações Microsoft Graph carregadas do banco!")
            else:
                print("ℹ️ Nenhuma configuração Microsoft Graph encontrada no banco")

            db.close()
        except Exception as e:
            print(f"⚠️ Erro ao carregar configurações do banco: {e}")

    except Exception as e:
        print(f"❌ Erro ao criar tabelas: {e}")

    yield

    # Shutdown
    print("🛑 Encerrando Amvox Omnichannel API...")

app = FastAPI(
    title="Amvox Omnichannel API",
    description="API para o sistema Omnichannel da Amvox",
    version="0.1.0",
    lifespan=lifespan
)

# Configuração de CORS para permitir requisições do frontend
app.add_middleware(
    CORSMiddleware,
    allow_origins=[
        "http://localhost:3000",
        "http://localhost:3001",
        "http://localhost",
        "http://127.0.0.1:3000",
        "http://127.0.0.1",
        "*"  # Permitir todas as origens em desenvolvimento
    ],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

@app.get("/")
async def root():
    """Endpoint raiz para verificar se a API está funcionando."""
    return {
        "message": "Bem-vindo à API do Sistema Omnichannel Amvox",
        "status": "online"
    }

@app.get("/health")
async def health_check():
    """Endpoint para verificar a saúde da API."""
    return {
        "status": "healthy",
        "version": "0.1.0"
    }

# Incluir os routers da API
app.include_router(auth.router, prefix="/api/v1")
app.include_router(threecx.router, prefix="/api/v1")
app.include_router(reports.router, prefix="/api/v1")
app.include_router(pdf_reports.router, prefix="/api/v1")
app.include_router(outlook.router, prefix="/api/v1")
app.include_router(whatsapp.router, prefix="/api/v1")
app.include_router(mock_data.router, prefix="/api/v1")
