"""
Script para inicializar o banco de dados e criar usuário administrador.
"""

import sys
import os
from sqlalchemy.orm import Session
from sqlalchemy import text

# Adicionar o diretório raiz ao path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from app.database.connection import engine, SessionLocal, create_tables
from app.database.models import Usuario, NivelUsuario, ConfiguracaoSistema
from app.crud.usuario import usuario_crud
from app.schemas.usuario import UsuarioCreate
from app.database.migrate_configuracoes import run_migration

def init_database():
    """Inicializar banco de dados e criar tabelas."""
    print("🔄 Criando tabelas no banco de dados...")
    try:
        create_tables()
        print("✅ Tabelas criadas com sucesso!")
        return True
    except Exception as e:
        print(f"❌ Erro ao criar tabelas: {e}")
        return False

def create_admin_user():
    """Criar usuário administrador padrão."""
    print("🔄 Criando usuário administrador...")

    db = SessionLocal()
    try:
        # Verificar se já existe um admin
        existing_admin = usuario_crud.get_by_login(db, "admin")
        if existing_admin:
            print("⚠️  Usuário administrador já existe!")
            print(f"   Login: {existing_admin.login}")
            print(f"   Nome: {existing_admin.nome_completo}")
            print(f"   Email: {existing_admin.email_corporativo}")
            return existing_admin

        # Criar usuário administrador
        admin_data = UsuarioCreate(
            nome="Administrador",
            sobrenome="Teste",
            login="admin",
            senha="admin123",
            nivel_usuario="administrador",
            email_corporativo="<EMAIL>",
            senha_email_corporativo="@mv0x@posvenda",
            ativo=True
        )

        admin_user = usuario_crud.create(db, usuario_data=admin_data)
        print("✅ Usuário administrador criado com sucesso!")
        print(f"   ID: {admin_user.id}")
        print(f"   Login: {admin_user.login}")
        print(f"   Nome: {admin_user.nome_completo}")
        print(f"   Nível: {admin_user.nivel_usuario.value}")
        print(f"   Email: {admin_user.email_corporativo}")
        print(f"   Criado em: {admin_user.criado_em}")

        return admin_user

    except Exception as e:
        print(f"❌ Erro ao criar usuário administrador: {e}")
        db.rollback()
        return None
    finally:
        db.close()

def verify_database():
    """Verificar se o banco está funcionando corretamente."""
    print("🔄 Verificando conexão com o banco de dados...")

    db = SessionLocal()
    try:
        # Testar conexão
        result = db.execute(text("SELECT 1"))
        print("✅ Conexão com banco de dados OK!")

        # Contar usuários
        user_count = db.query(Usuario).count()
        print(f"📊 Total de usuários no banco: {user_count}")

        # Listar usuários
        if user_count > 0:
            users = db.query(Usuario).all()
            print("👥 Usuários cadastrados:")
            for user in users:
                status = "🟢 Ativo" if user.ativo else "🔴 Inativo"
                print(f"   - {user.nome_completo} ({user.login}) - {user.nivel_usuario.value} {status}")

        return True

    except Exception as e:
        print(f"❌ Erro ao verificar banco: {e}")
        return False
    finally:
        db.close()

def main():
    """Função principal para inicializar o sistema."""
    print("🚀 Inicializando sistema de banco de dados Amvox Omnichannel")
    print("=" * 60)

    # 1. Criar tabelas
    if not init_database():
        print("❌ Falha ao inicializar banco de dados!")
        return False

    # 2. Verificar conexão
    if not verify_database():
        print("❌ Falha ao verificar banco de dados!")
        return False

    # 3. Executar migração das configurações
    print("🔄 Executando migração das configurações...")
    if not run_migration():
        print("❌ Falha ao executar migração das configurações!")
        return False

    # 4. Criar usuário administrador
    admin_user = create_admin_user()
    if not admin_user:
        print("❌ Falha ao criar usuário administrador!")
        return False

    print("=" * 60)
    print("🎉 Sistema inicializado com sucesso!")
    print("\n📋 Credenciais do administrador:")
    print(f"   Login: admin")
    print(f"   Senha: admin123")
    print(f"   Email: <EMAIL>")
    print("\n⚠️  IMPORTANTE: Altere a senha padrão após o primeiro login!")

    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
