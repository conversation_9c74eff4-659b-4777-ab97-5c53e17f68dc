"""
Schemas Pydantic para WhatsApp.
"""

from datetime import datetime
from typing import Optional, List
from pydantic import BaseModel, Field, validator
from enum import Enum


class StatusMensagemEnum(str, Enum):
    """Status da mensagem WhatsApp."""
    ENVIADA = "enviada"
    ENTREGUE = "entregue"
    LIDA = "lida"
    ERRO = "erro"


class TipoMensagemEnum(str, Enum):
    """Tipo da mensagem WhatsApp."""
    TEXTO = "texto"
    IMAGEM = "imagem"
    AUDIO = "audio"
    VIDEO = "video"
    DOCUMENTO = "documento"
    STICKER = "sticker"
    LOCALIZACAO = "localizacao"


class StatusConversaEnum(str, Enum):
    """Status da conversa WhatsApp."""
    ATIVA = "ativa"
    AGUARDANDO = "aguardando"
    FINALIZADA = "finalizada"
    TRANSFERIDA = "transferida"


# ==================== WEBHOOK SCHEMAS ====================

class EvolutionWebhookMessage(BaseModel):
    """Schema para mensagem recebida via webhook da Evolution API."""
    key: dict = Field(..., description="Chave da mensagem")
    pushName: Optional[str] = Field(None, description="Nome do remetente")
    message: dict = Field(..., description="Conteúdo da mensagem")
    messageTimestamp: int = Field(..., description="Timestamp da mensagem")
    status: Optional[str] = Field(None, description="Status da mensagem")
    
    @validator('key')
    def validate_key(cls, v):
        """Validar estrutura da chave."""
        required_fields = ['remoteJid', 'id']
        for field in required_fields:
            if field not in v:
                raise ValueError(f"Campo '{field}' é obrigatório na chave")
        return v


class EvolutionWebhookData(BaseModel):
    """Schema para dados do webhook da Evolution API."""
    key: dict = Field(..., description="Chave da mensagem")
    pushName: Optional[str] = Field(None, description="Nome do remetente")
    message: dict = Field(..., description="Conteúdo da mensagem")
    messageTimestamp: int = Field(..., description="Timestamp da mensagem")
    owner: str = Field(..., description="Proprietário da instância")
    source: str = Field(..., description="Fonte da mensagem")


class EvolutionWebhookPayload(BaseModel):
    """Schema para payload completo do webhook da Evolution API."""
    event: str = Field(..., description="Tipo do evento")
    instance: str = Field(..., description="Nome da instância")
    data: EvolutionWebhookData = Field(..., description="Dados da mensagem")


# ==================== MENSAGEM SCHEMAS ====================

class WhatsAppMensagemBase(BaseModel):
    """Schema base para mensagem WhatsApp."""
    tipo: TipoMensagemEnum = Field(TipoMensagemEnum.TEXTO, description="Tipo da mensagem")
    conteudo: Optional[str] = Field(None, description="Conteúdo textual da mensagem")
    midia_url: Optional[str] = Field(None, description="URL da mídia")
    midia_tipo: Optional[str] = Field(None, description="Tipo MIME da mídia")
    de_contato: bool = Field(True, description="Se foi enviada pelo contato")
    nome_remetente: Optional[str] = Field(None, description="Nome do remetente")


class WhatsAppMensagemCreate(WhatsAppMensagemBase):
    """Schema para criação de mensagem WhatsApp."""
    message_id: str = Field(..., description="ID único da mensagem")
    remote_jid: str = Field(..., description="JID remoto")
    timestamp_evolution: datetime = Field(..., description="Timestamp da Evolution API")


class WhatsAppMensagemResponse(WhatsAppMensagemBase):
    """Schema para resposta de mensagem WhatsApp."""
    id: int
    conversa_id: int
    message_id: str
    remote_jid: str
    status: StatusMensagemEnum
    lida: bool
    timestamp_evolution: datetime
    criada_em: datetime
    lida_em: Optional[datetime] = None
    
    class Config:
        from_attributes = True


# ==================== CONVERSA SCHEMAS ====================

class WhatsAppConversaBase(BaseModel):
    """Schema base para conversa WhatsApp."""
    numero_contato: str = Field(..., description="Número do contato")
    nome_contato: Optional[str] = Field(None, description="Nome do contato")
    instancia_id: str = Field(..., description="ID da instância")


class WhatsAppConversaCreate(WhatsAppConversaBase):
    """Schema para criação de conversa WhatsApp."""
    pass


class WhatsAppConversaUpdate(BaseModel):
    """Schema para atualização de conversa WhatsApp."""
    status: Optional[StatusConversaEnum] = None
    colaborador_id: Optional[int] = None
    nome_contato: Optional[str] = None


class WhatsAppConversaResponse(WhatsAppConversaBase):
    """Schema para resposta de conversa WhatsApp."""
    id: int
    status: StatusConversaEnum
    colaborador_id: Optional[int] = None
    ultima_mensagem: Optional[str] = None
    ultima_mensagem_em: Optional[datetime] = None
    mensagens_nao_lidas: int
    criada_em: datetime
    atualizada_em: datetime
    finalizada_em: Optional[datetime] = None
    
    # Dados do colaborador (se atribuído)
    colaborador_nome: Optional[str] = None
    
    # Últimas mensagens
    mensagens: List[WhatsAppMensagemResponse] = []
    
    class Config:
        from_attributes = True


# ==================== ENVIO DE MENSAGEM SCHEMAS ====================

class EnviarMensagemRequest(BaseModel):
    """Schema para envio de mensagem WhatsApp."""
    numero: str = Field(..., description="Número do destinatário (formato internacional)")
    mensagem: str = Field(..., min_length=1, max_length=4096, description="Texto da mensagem")
    instancia_id: Optional[str] = Field(None, description="ID da instância (opcional)")
    
    @validator('numero')
    def validate_numero(cls, v):
        """Validar formato do número."""
        # Remover caracteres especiais
        numero_limpo = ''.join(filter(str.isdigit, v))
        
        # Verificar se tem pelo menos 10 dígitos
        if len(numero_limpo) < 10:
            raise ValueError('Número deve ter pelo menos 10 dígitos')
        
        # Adicionar código do país se não tiver
        if not numero_limpo.startswith('55'):
            numero_limpo = '55' + numero_limpo
            
        return numero_limpo


class EnviarMensagemResponse(BaseModel):
    """Schema para resposta de envio de mensagem."""
    sucesso: bool
    message_id: Optional[str] = None
    erro: Optional[str] = None
    numero_formatado: str


# ==================== INSTÂNCIA SCHEMAS ====================

class WhatsAppInstanciaBase(BaseModel):
    """Schema base para instância WhatsApp."""
    nome: str = Field(..., description="Nome da instância")
    instancia_id: str = Field(..., description="ID da instância")
    api_key: str = Field(..., description="API Key")
    base_url: str = Field(..., description="URL base da Evolution API")


class WhatsAppInstanciaCreate(WhatsAppInstanciaBase):
    """Schema para criação de instância WhatsApp."""
    webhook_url: Optional[str] = None


class WhatsAppInstanciaResponse(WhatsAppInstanciaBase):
    """Schema para resposta de instância WhatsApp."""
    id: int
    webhook_url: Optional[str] = None
    ativa: bool
    conectada: bool
    ultimo_status: Optional[str] = None
    criada_em: datetime
    atualizada_em: datetime
    
    class Config:
        from_attributes = True


# ==================== ATRIBUIÇÃO SCHEMAS ====================

class AtribuirConversaRequest(BaseModel):
    """Schema para atribuir conversa a colaborador."""
    colaborador_id: int = Field(..., description="ID do colaborador")


class AtribuirConversaResponse(BaseModel):
    """Schema para resposta de atribuição."""
    sucesso: bool
    conversa_id: int
    colaborador_id: int
    colaborador_nome: str


# ==================== LISTAGEM SCHEMAS ====================

class ListarConversasResponse(BaseModel):
    """Schema para listagem de conversas."""
    conversas: List[WhatsAppConversaResponse]
    total: int
    pagina: int
    por_pagina: int
    total_paginas: int
