"""
Endpoints da API para integração com WhatsApp.
"""

import logging
from typing import List, Optional
from fastapi import APIRouter, HTTPException, Depends, Request, BackgroundTasks
from fastapi.responses import JSONResponse
from sqlalchemy.orm import Session
import math

from app.database.connection import get_db
from app.services.integrations.whatsapp import WhatsAppService
from app.schemas.whatsapp import (
    EvolutionWebhookPayload,
    WhatsAppConversaResponse,
    EnviarMensagemRequest,
    EnviarMensagemResponse,
    AtribuirConversaRequest,
    AtribuirConversaResponse,
    ListarConversasResponse,
    StatusConversaEnum
)
from app.models.whatsapp import StatusConversa
from app.utils.auth import get_current_user
from app.models.usuario import Usuario

logger = logging.getLogger(__name__)

router = APIRouter(
    prefix="/whatsapp",
    tags=["WhatsApp"],
    responses={404: {"description": "Not found"}},
)


# ==================== WEBHOOK ENDPOINT ====================

@router.post("/webhook")
async def webhook_evolution(
    request: Request,
    background_tasks: BackgroundTasks,
    db: Session = Depends(get_db)
):
    """
    Webhook para receber mensagens da Evolution API.

    Este endpoint recebe as mensagens enviadas pela Evolution API
    e as processa em background para não bloquear a resposta.
    """
    try:
        # Obter dados do webhook
        webhook_data = await request.json()

        logger.info(f"Webhook recebido: {webhook_data}")

        # Validar estrutura básica
        if 'event' not in webhook_data or 'data' not in webhook_data:
            logger.warning("Webhook com estrutura inválida")
            return JSONResponse(
                status_code=400,
                content={"error": "Estrutura de webhook inválida"}
            )

        # Processar apenas eventos de mensagem
        event = webhook_data.get('event')
        if event not in ['messages.upsert', 'MESSAGES_UPSERT']:
            logger.debug(f"Evento ignorado: {event}")
            return JSONResponse(
                status_code=200,
                content={"status": "ignored", "event": event}
            )

        # Adicionar processamento em background
        background_tasks.add_task(
            process_webhook_message,
            webhook_data,
            db
        )

        return JSONResponse(
            status_code=200,
            content={"status": "received", "event": event}
        )

    except Exception as e:
        logger.error(f"Erro no webhook: {e}")
        return JSONResponse(
            status_code=500,
            content={"error": "Erro interno do servidor"}
        )


async def process_webhook_message(webhook_data: dict, db: Session):
    """
    Processa a mensagem do webhook em background.

    Args:
        webhook_data: Dados do webhook
        db: Sessão do banco de dados
    """
    try:
        # Criar payload validado
        payload = EvolutionWebhookPayload(**webhook_data)

        # Processar mensagem
        whatsapp_service = WhatsAppService(db)
        mensagem = whatsapp_service.process_webhook_message(payload)

        if mensagem:
            logger.info(f"Mensagem processada com sucesso: {mensagem.id}")
        else:
            logger.warning("Falha ao processar mensagem do webhook")

    except Exception as e:
        logger.error(f"Erro ao processar webhook em background: {e}")


# ==================== CONVERSAS ENDPOINTS ====================

@router.get("/conversas", response_model=ListarConversasResponse)
async def listar_conversas(
    page: int = 1,
    per_page: int = 20,
    status: Optional[StatusConversaEnum] = None,
    colaborador_id: Optional[int] = None,
    db: Session = Depends(get_db),
    current_user: Usuario = Depends(get_current_user)
):
    """
    Lista conversas do WhatsApp com paginação.

    Args:
        page: Página atual (padrão: 1)
        per_page: Itens por página (padrão: 20, máximo: 100)
        status: Filtrar por status da conversa
        colaborador_id: Filtrar por colaborador atribuído
    """
    try:
        # Validar parâmetros
        if per_page > 100:
            per_page = 100
        if page < 1:
            page = 1

        # Converter status se fornecido
        status_filter = None
        if status:
            status_filter = StatusConversa(status.value)

        # Listar conversas
        whatsapp_service = WhatsAppService(db)
        conversas, total = whatsapp_service.listar_conversas(
            page=page,
            per_page=per_page,
            status=status_filter,
            colaborador_id=colaborador_id
        )

        # Calcular total de páginas
        total_pages = math.ceil(total / per_page)

        # Converter para response
        conversas_response = []
        for conversa in conversas:
            conversa_dict = {
                "id": conversa.id,
                "numero_contato": conversa.numero_contato,
                "nome_contato": conversa.nome_contato,
                "instancia_id": conversa.instancia_id,
                "status": conversa.status.value,
                "colaborador_id": conversa.colaborador_id,
                "ultima_mensagem": conversa.ultima_mensagem,
                "ultima_mensagem_em": conversa.ultima_mensagem_em,
                "mensagens_nao_lidas": conversa.mensagens_nao_lidas,
                "criada_em": conversa.criada_em,
                "atualizada_em": conversa.atualizada_em,
                "finalizada_em": conversa.finalizada_em,
                "colaborador_nome": conversa.colaborador.nome if conversa.colaborador else None,
                "mensagens": []
            }
            conversas_response.append(WhatsAppConversaResponse(**conversa_dict))

        return ListarConversasResponse(
            conversas=conversas_response,
            total=total,
            pagina=page,
            por_pagina=per_page,
            total_paginas=total_pages
        )

    except Exception as e:
        logger.error(f"Erro ao listar conversas: {e}")
        raise HTTPException(status_code=500, detail="Erro interno do servidor")


@router.get("/conversas/{conversa_id}", response_model=WhatsAppConversaResponse)
async def obter_conversa(
    conversa_id: int,
    limit_mensagens: int = 50,
    db: Session = Depends(get_db),
    current_user: Usuario = Depends(get_current_user)
):
    """
    Obtém uma conversa específica com suas mensagens.

    Args:
        conversa_id: ID da conversa
        limit_mensagens: Limite de mensagens a carregar (padrão: 50)
    """
    try:
        whatsapp_service = WhatsAppService(db)
        conversa = whatsapp_service.get_conversa_with_messages(
            conversa_id=conversa_id,
            limit=limit_mensagens
        )

        if not conversa:
            raise HTTPException(status_code=404, detail="Conversa não encontrada")

        # Marcar mensagens como lidas
        whatsapp_service.marcar_mensagens_como_lidas(conversa_id)

        # Converter mensagens
        mensagens_response = []
        for mensagem in conversa.mensagens:
            mensagem_dict = {
                "id": mensagem.id,
                "conversa_id": mensagem.conversa_id,
                "message_id": mensagem.message_id,
                "remote_jid": mensagem.remote_jid,
                "tipo": mensagem.tipo.value,
                "conteudo": mensagem.conteudo,
                "midia_url": mensagem.midia_url,
                "midia_tipo": mensagem.midia_tipo,
                "de_contato": mensagem.de_contato,
                "nome_remetente": mensagem.nome_remetente,
                "status": mensagem.status.value,
                "lida": mensagem.lida,
                "timestamp_evolution": mensagem.timestamp_evolution,
                "criada_em": mensagem.criada_em,
                "lida_em": mensagem.lida_em
            }
            mensagens_response.append(mensagem_dict)

        # Converter conversa
        conversa_dict = {
            "id": conversa.id,
            "numero_contato": conversa.numero_contato,
            "nome_contato": conversa.nome_contato,
            "instancia_id": conversa.instancia_id,
            "status": conversa.status.value,
            "colaborador_id": conversa.colaborador_id,
            "ultima_mensagem": conversa.ultima_mensagem,
            "ultima_mensagem_em": conversa.ultima_mensagem_em,
            "mensagens_nao_lidas": 0,  # Zerado pois marcamos como lidas
            "criada_em": conversa.criada_em,
            "atualizada_em": conversa.atualizada_em,
            "finalizada_em": conversa.finalizada_em,
            "colaborador_nome": conversa.colaborador.nome if conversa.colaborador else None,
            "mensagens": mensagens_response
        }

        return WhatsAppConversaResponse(**conversa_dict)

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Erro ao obter conversa: {e}")
        raise HTTPException(status_code=500, detail="Erro interno do servidor")


# ==================== ATRIBUIÇÃO ENDPOINTS ====================

@router.post("/conversas/{conversa_id}/atribuir", response_model=AtribuirConversaResponse)
async def atribuir_conversa(
    conversa_id: int,
    request: AtribuirConversaRequest,
    db: Session = Depends(get_db),
    current_user: Usuario = Depends(get_current_user)
):
    """
    Atribui uma conversa a um colaborador.

    Args:
        conversa_id: ID da conversa
        request: Dados da atribuição
    """
    try:
        whatsapp_service = WhatsAppService(db)
        conversa = whatsapp_service.atribuir_conversa(
            conversa_id=conversa_id,
            colaborador_id=request.colaborador_id
        )

        if not conversa:
            raise HTTPException(status_code=404, detail="Conversa ou colaborador não encontrado")

        return AtribuirConversaResponse(
            sucesso=True,
            conversa_id=conversa.id,
            colaborador_id=conversa.colaborador_id,
            colaborador_nome=conversa.colaborador.nome if conversa.colaborador else "Desconhecido"
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Erro ao atribuir conversa: {e}")
        raise HTTPException(status_code=500, detail="Erro interno do servidor")


@router.post("/conversas/{conversa_id}/finalizar")
async def finalizar_conversa(
    conversa_id: int,
    db: Session = Depends(get_db),
    current_user: Usuario = Depends(get_current_user)
):
    """
    Finaliza uma conversa.

    Args:
        conversa_id: ID da conversa
    """
    try:
        whatsapp_service = WhatsAppService(db)
        conversa = whatsapp_service.finalizar_conversa(conversa_id)

        if not conversa:
            raise HTTPException(status_code=404, detail="Conversa não encontrada")

        return {
            "sucesso": True,
            "conversa_id": conversa.id,
            "status": conversa.status.value,
            "finalizada_em": conversa.finalizada_em
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Erro ao finalizar conversa: {e}")
        raise HTTPException(status_code=500, detail="Erro interno do servidor")


# ==================== ENVIO DE MENSAGENS ENDPOINTS ====================

@router.post("/enviar", response_model=EnviarMensagemResponse)
async def enviar_mensagem(
    request: EnviarMensagemRequest,
    db: Session = Depends(get_db),
    current_user: Usuario = Depends(get_current_user)
):
    """
    Envia uma mensagem de texto via WhatsApp.

    Args:
        request: Dados da mensagem a enviar
    """
    try:
        whatsapp_service = WhatsAppService(db)
        response = await whatsapp_service.enviar_mensagem(
            request=request,
            colaborador_id=current_user.id
        )

        return response

    except Exception as e:
        logger.error(f"Erro ao enviar mensagem: {e}")
        raise HTTPException(status_code=500, detail="Erro interno do servidor")


@router.post("/conversas/{conversa_id}/enviar", response_model=EnviarMensagemResponse)
async def enviar_mensagem_conversa(
    conversa_id: int,
    mensagem: str,
    db: Session = Depends(get_db),
    current_user: Usuario = Depends(get_current_user)
):
    """
    Envia uma mensagem para uma conversa específica.

    Args:
        conversa_id: ID da conversa
        mensagem: Texto da mensagem
    """
    try:
        # Buscar conversa
        whatsapp_service = WhatsAppService(db)
        conversa = whatsapp_service.get_conversa_with_messages(conversa_id, limit=1)

        if not conversa:
            raise HTTPException(status_code=404, detail="Conversa não encontrada")

        # Criar request de envio
        request = EnviarMensagemRequest(
            numero=conversa.numero_contato,
            mensagem=mensagem,
            instancia_id=conversa.instancia_id
        )

        # Enviar mensagem
        response = await whatsapp_service.enviar_mensagem(
            request=request,
            colaborador_id=current_user.id
        )

        return response

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Erro ao enviar mensagem para conversa: {e}")
        raise HTTPException(status_code=500, detail="Erro interno do servidor")


# ==================== STATUS E INFORMAÇÕES ====================

@router.get("/status")
async def status_whatsapp(
    db: Session = Depends(get_db),
    current_user: Usuario = Depends(get_current_user)
):
    """
    Obtém status geral do WhatsApp.
    """
    try:
        from app.models.whatsapp import WhatsAppInstancia, WhatsAppConversa, WhatsAppMensagem

        # Contar instâncias
        total_instancias = db.query(WhatsAppInstancia).count()
        instancias_ativas = db.query(WhatsAppInstancia).filter(
            WhatsAppInstancia.ativa == True
        ).count()

        # Contar conversas
        total_conversas = db.query(WhatsAppConversa).count()
        conversas_ativas = db.query(WhatsAppConversa).filter(
            WhatsAppConversa.status == StatusConversa.ATIVA
        ).count()
        conversas_aguardando = db.query(WhatsAppConversa).filter(
            WhatsAppConversa.status == StatusConversa.AGUARDANDO
        ).count()

        # Contar mensagens não lidas
        mensagens_nao_lidas = db.query(WhatsAppMensagem).filter(
            WhatsAppMensagem.lida == False,
            WhatsAppMensagem.de_contato == True
        ).count()

        return {
            "instancias": {
                "total": total_instancias,
                "ativas": instancias_ativas
            },
            "conversas": {
                "total": total_conversas,
                "ativas": conversas_ativas,
                "aguardando": conversas_aguardando
            },
            "mensagens_nao_lidas": mensagens_nao_lidas
        }

    except Exception as e:
        logger.error(f"Erro ao obter status: {e}")
        raise HTTPException(status_code=500, detail="Erro interno do servidor")
