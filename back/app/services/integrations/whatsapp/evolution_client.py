"""
Cliente para integração com Evolution API.
"""

import httpx
import logging
from typing import Dict, Any, Optional
from datetime import datetime

logger = logging.getLogger(__name__)


class EvolutionAPIClient:
    """Cliente para interagir com a Evolution API."""
    
    def __init__(self, base_url: str, api_key: str, instance_name: str):
        """
        Inicializa o cliente Evolution API.
        
        Args:
            base_url: URL base da Evolution API
            api_key: Chave de API para autenticação
            instance_name: Nome da instância
        """
        self.base_url = base_url.rstrip('/')
        self.api_key = api_key
        self.instance_name = instance_name
        self.headers = {
            "Content-Type": "application/json",
            "apikey": api_key
        }
    
    async def _make_request(
        self, 
        method: str, 
        endpoint: str, 
        data: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """
        Faz uma requisição para a Evolution API.
        
        Args:
            method: Método HTTP (GET, POST, etc.)
            endpoint: Endpoint da API
            data: Dados para enviar (se aplicável)
            
        Returns:
            Resposta da API
            
        Raises:
            httpx.HTTPError: Se a requisição falhar
        """
        url = f"{self.base_url}{endpoint}"
        
        logger.info(f"Evolution API {method} {url}")
        
        async with httpx.AsyncClient(timeout=30.0) as client:
            try:
                if method.upper() == "GET":
                    response = await client.get(url, headers=self.headers)
                elif method.upper() == "POST":
                    response = await client.post(url, headers=self.headers, json=data)
                elif method.upper() == "PUT":
                    response = await client.put(url, headers=self.headers, json=data)
                elif method.upper() == "DELETE":
                    response = await client.delete(url, headers=self.headers)
                else:
                    raise ValueError(f"Método HTTP não suportado: {method}")
                
                response.raise_for_status()
                
                # Tentar fazer parse do JSON
                try:
                    result = response.json()
                    logger.debug(f"Evolution API response: {result}")
                    return result
                except Exception:
                    # Se não for JSON válido, retornar texto
                    return {"text": response.text, "status_code": response.status_code}
                    
            except httpx.HTTPError as e:
                logger.error(f"Erro na requisição Evolution API: {e}")
                raise
            except Exception as e:
                logger.error(f"Erro inesperado na Evolution API: {e}")
                raise
    
    async def send_text_message(self, number: str, message: str) -> Dict[str, Any]:
        """
        Envia uma mensagem de texto.
        
        Args:
            number: Número do destinatário (formato internacional)
            message: Texto da mensagem
            
        Returns:
            Resposta da API com informações da mensagem enviada
        """
        # Formatar número para o padrão WhatsApp
        formatted_number = number
        if not formatted_number.endswith("@s.whatsapp.net"):
            formatted_number = f"{formatted_number}@s.whatsapp.net"
        
        data = {
            "number": formatted_number,
            "text": message
        }
        
        endpoint = f"/message/sendText/{self.instance_name}"
        return await self._make_request("POST", endpoint, data)
    
    async def send_media_message(
        self, 
        number: str, 
        media_url: str, 
        caption: Optional[str] = None,
        media_type: str = "image"
    ) -> Dict[str, Any]:
        """
        Envia uma mensagem de mídia.
        
        Args:
            number: Número do destinatário
            media_url: URL da mídia
            caption: Legenda da mídia (opcional)
            media_type: Tipo da mídia (image, video, audio, document)
            
        Returns:
            Resposta da API
        """
        # Formatar número
        formatted_number = number
        if not formatted_number.endswith("@s.whatsapp.net"):
            formatted_number = f"{formatted_number}@s.whatsapp.net"
        
        data = {
            "number": formatted_number,
            "media": media_url
        }
        
        if caption:
            data["caption"] = caption
        
        # Escolher endpoint baseado no tipo de mídia
        endpoint_map = {
            "image": f"/message/sendMedia/{self.instance_name}",
            "video": f"/message/sendMedia/{self.instance_name}",
            "audio": f"/message/sendWhatsAppAudio/{self.instance_name}",
            "document": f"/message/sendMedia/{self.instance_name}"
        }
        
        endpoint = endpoint_map.get(media_type, f"/message/sendMedia/{self.instance_name}")
        return await self._make_request("POST", endpoint, data)
    
    async def get_instance_info(self) -> Dict[str, Any]:
        """
        Obtém informações da instância.
        
        Returns:
            Informações da instância
        """
        endpoint = f"/instance/fetchInstances"
        return await self._make_request("GET", endpoint)
    
    async def get_instance_status(self) -> Dict[str, Any]:
        """
        Obtém status da instância.
        
        Returns:
            Status da instância
        """
        endpoint = f"/instance/connectionState/{self.instance_name}"
        return await self._make_request("GET", endpoint)
    
    async def set_webhook(self, webhook_url: str) -> Dict[str, Any]:
        """
        Configura o webhook da instância.
        
        Args:
            webhook_url: URL do webhook
            
        Returns:
            Resposta da configuração
        """
        data = {
            "webhook": {
                "url": webhook_url,
                "events": [
                    "MESSAGES_UPSERT",
                    "MESSAGES_UPDATE", 
                    "SEND_MESSAGE",
                    "CONNECTION_UPDATE"
                ]
            }
        }
        
        endpoint = f"/webhook/set/{self.instance_name}"
        return await self._make_request("POST", endpoint, data)
    
    async def create_instance(self, webhook_url: Optional[str] = None) -> Dict[str, Any]:
        """
        Cria uma nova instância.
        
        Args:
            webhook_url: URL do webhook (opcional)
            
        Returns:
            Resposta da criação
        """
        data = {
            "instanceName": self.instance_name,
            "token": self.api_key
        }
        
        if webhook_url:
            data["webhook"] = {
                "url": webhook_url,
                "events": [
                    "MESSAGES_UPSERT",
                    "MESSAGES_UPDATE",
                    "SEND_MESSAGE", 
                    "CONNECTION_UPDATE"
                ]
            }
        
        endpoint = "/instance/create"
        return await self._make_request("POST", endpoint, data)
    
    async def delete_instance(self) -> Dict[str, Any]:
        """
        Deleta a instância.
        
        Returns:
            Resposta da deleção
        """
        endpoint = f"/instance/delete/{self.instance_name}"
        return await self._make_request("DELETE", endpoint)
    
    async def get_qr_code(self) -> Dict[str, Any]:
        """
        Obtém o QR Code para conectar ao WhatsApp.
        
        Returns:
            QR Code em base64
        """
        endpoint = f"/instance/connect/{self.instance_name}"
        return await self._make_request("GET", endpoint)
    
    async def logout_instance(self) -> Dict[str, Any]:
        """
        Faz logout da instância.
        
        Returns:
            Resposta do logout
        """
        endpoint = f"/instance/logout/{self.instance_name}"
        return await self._make_request("DELETE", endpoint)
    
    async def restart_instance(self) -> Dict[str, Any]:
        """
        Reinicia a instância.
        
        Returns:
            Resposta do restart
        """
        endpoint = f"/instance/restart/{self.instance_name}"
        return await self._make_request("PUT", endpoint)
