#!/usr/bin/env python3
"""
Script para criar usuário administrador padrão.
Este script é executado durante o deploy para garantir que sempre existe um admin.
"""

import sys
import os
from sqlalchemy.orm import Session
from sqlalchemy import text

# Adicionar o diretório raiz ao path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from app.database.connection import SessionLocal, engine
from app.database.models import <PERSON><PERSON><PERSON>, NivelUsuario
from app.utils.security import hash_password

def create_admin_user():
    """Criar usuário administrador padrão."""
    try:
        # Criar sessão do banco
        db = SessionLocal()
        
        print("🔍 Verificando usuário admin existente...")
        
        # Verificar se já existe usuário admin
        existing_admin = db.query(Usuario).filter(Usuario.login == "admin").first()
        
        if existing_admin:
            print("🗑️ Removendo usuário admin existente...")
            db.delete(existing_admin)
            db.commit()
        
        # Criar novo usuário admin
        print("➕ Criando novo usuário administrador...")
        
        admin_user = Usuario(
            nome="Administrador",
            sobrenome="Sistema",
            login="admin",
            senha=hash_password("secret"),  # Senha: secret
            nivel_usuario=NivelUsuario.ADMINISTRADOR,
            email_corporativo="<EMAIL>",
            senha_email_corporativo="admin123",
            ativo=True
        )
        
        db.add(admin_user)
        db.commit()
        db.refresh(admin_user)
        
        print(f"✅ Usuário admin criado com sucesso! ID: {admin_user.id}")
        print(f"   Login: admin")
        print(f"   Senha: secret")
        print(f"   Email: {admin_user.email_corporativo}")
        print(f"   Nível: {admin_user.nivel_usuario.value}")
        
        # Verificar se foi criado corretamente
        verification = db.query(Usuario).filter(Usuario.login == "admin").first()
        if verification:
            print("🔍 Verificação: Usuário admin encontrado no banco!")
        else:
            print("❌ Erro: Usuário admin não foi encontrado após criação!")
            return False
            
        db.close()
        return True
        
    except Exception as e:
        print(f"❌ Erro ao criar usuário admin: {e}")
        if 'db' in locals():
            db.rollback()
            db.close()
        return False

def check_database_connection():
    """Verificar conexão com o banco de dados."""
    try:
        print("🔗 Verificando conexão com o banco de dados...")
        with engine.connect() as connection:
            result = connection.execute(text("SELECT 1"))
            if result.fetchone():
                print("✅ Conexão com o banco estabelecida!")
                return True
    except Exception as e:
        print(f"❌ Erro de conexão com o banco: {e}")
        return False

def main():
    """Função principal."""
    print("👤 Iniciando criação do usuário administrador...")
    
    # Verificar conexão com o banco
    if not check_database_connection():
        sys.exit(1)
    
    # Criar usuário admin
    if create_admin_user():
        print("🎉 Usuário administrador criado com sucesso!")
        sys.exit(0)
    else:
        print("❌ Falha ao criar usuário administrador!")
        sys.exit(1)

if __name__ == "__main__":
    main()
